{"version": 3, "sources": ["tabulator_midnight.scss"], "names": [], "mappings": "AAAA,WAEE,qBAAsB,CAEtB,cAAe,CAEf,eAAgB,CALhB,iBAAkB,CAIlB,eAAgB,CAEhB,+BAAgC,CAChC,4BAA6B,CAC7B,2BAA4B,CAC5B,0BAA2B,CAC3B,uBAA0B,CAC1B,iFACE,cAAiB,CACnB,0CACE,oBAAuB,CAGzB,sGACE,gBAAmB,CACrB,6BAKE,qBAAsB,CADtB,4BAA6B,CAF7B,qBAAsB,CAItB,UAAW,CACX,eAAiB,CAOjB,YAAa,CALb,eAAgB,CARhB,iBAAkB,CASlB,qBAAsB,CACtB,uBAAwB,CACxB,wBAAyB,CACzB,mBAAoB,CALpB,kBAAmB,CALnB,UAWe,CACf,qDACE,YAAe,CACjB,wDAEE,eAAgB,CADhB,iBACkB,CAClB,2EACE,oBAAuB,CAC3B,4CAOE,eAAgB,CADhB,2BAA4B,CAH5B,qBAAsB,CAFtB,mBAAoB,CAGpB,qBAAsB,CACtB,0BAA2B,CAK3B,eAAgB,CARhB,iBAAkB,CAMlB,eAAgB,CAChB,qBACkB,CAClB,6DAGE,kBAAmB,CADnB,qBAAsB,CAEtB,mBAAoB,CAHpB,iBAGsB,CACxB,sEACE,qBAAsB,CACtB,UAAgB,CAClB,qEACE,qBAAsB,CACtB,UAAa,CACf,mEACE,qBAAsB,CAEtB,WAAY,CADZ,iBACc,CACd,kGACE,aAAgB,CAChB,wGACE,cAAe,CACf,UAAa,CACjB,+FACE,iBAAoB,CACtB,wFACE,qBAAsB,CAGtB,eAAgB,CAChB,sBAAuB,CACvB,qBAAsB,CAHtB,kBAAmB,CADnB,UAIwB,CACxB,iHAEE,kBAAsB,CADtB,kBACwB,CAC1B,gHAKE,eAAgB,CAFhB,qBAAsB,CAFtB,qBAAsB,CAGtB,WAAY,CAFZ,UAGkB,CACpB,+IACE,uBAA0B,CAC9B,yFAEE,kBAAmB,CAGnB,QAAS,CAJT,YAAa,CAEb,iBAAkB,CAGlB,SAAU,CAFV,KAEY,CACZ,0GAKE,4BAA6B,CAF7B,iCAAkC,CAClC,kCAAmC,CAFnC,QAAS,CADT,OAI+B,CACrC,0FAGE,yBAA0B,CAD1B,YAAa,CAGb,iBAAkB,CADlB,eAAgB,CAHhB,iBAIoB,CACtB,qEAEE,qBAAsB,CACtB,cAAe,CAFf,iBAAkB,CAIlB,iBAAkB,CADlB,UACoB,CACpB,8EACE,qBAAyB,CAC3B,yEACE,cAAiB,CACnB,sFAEE,QAAS,CADT,OACW,CACf,oFACE,kBAAqB,CACvB,wCACE,kGAEE,wBAAyB,CADzB,cAC2B,CAAE,CACjC,4HACE,UAAa,CACb,wCACE,gLAEE,4BAA6B,CAD7B,cAC+B,CAAE,CACrC,6IAEE,4BAA6B,CAD7B,eAC+B,CACnC,iIACE,UAAa,CACb,wCACE,qLAEE,4BAA6B,CAD7B,cAC+B,CAAE,CACrC,kJAEE,4BAA6B,CAD7B,eAC+B,CACnC,kIACE,UAAa,CACb,wCACE,sLAEE,yBAA0B,CAD1B,cAC4B,CAAE,CAClC,mJACE,kBAAmB,CACnB,yBAA0B,CAC1B,UAAa,CACjB,+GAIE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CAHvB,sBAAuB,CADvB,wBAIyB,CAC3B,oHACE,wBAA2B,CAC7B,2GACE,eAAgB,CAChB,gBAAmB,CACrB,uIAEE,mBAAoB,CADpB,eACsB,CACxB,4GAKE,WAAY,CAJZ,sBAAuB,CACvB,MAAO,CACP,OAAQ,CACR,OACc,CAClB,+CAEE,MAAO,CADP,eAAgB,CAEhB,UAAa,CACb,qEACE,2BAA8B,CAChC,sEACE,0BAA6B,CACjC,qDAGE,4BAA8B,CAE9B,4BAA6B,CAD7B,yBAA0B,CAH1B,qBAAsB,CACtB,oBAG+B,CAC/B,oEACE,4BAAgC,CAChC,iGACE,YAAe,CACrB,2DACE,oBAAuB,CACvB,iEACE,YAAe,CACrB,kCAKE,gCAAiC,CADjC,aAAc,CAHd,iBAAkB,CAElB,kBAAmB,CADnB,UAGmC,CACnC,wCACE,YAAe,CACjB,yDAGE,kBAAmB,CAFnB,qBAAsB,CACtB,YAAa,CAEb,sBAAuB,CACvB,cAAe,CACf,UAAa,CACb,wFACE,eAAkB,CACpB,yFAIE,UAAW,CAHX,oBAAqB,CAKrB,cAAe,CADf,eAAiB,CAFjB,YAAa,CADb,iBAAkB,CAKlB,kBAAqB,CACzB,mDAGE,qBAAsB,CAGtB,UAAW,CAJX,oBAAqB,CAGrB,gBAAiB,CAJjB,iBAAkB,CAGlB,kBAEa,CACb,kFAEE,4BAA8B,CAD9B,eACgC,CAChC,sGACE,4BAA+B,CACjC,yGACE,yBAA4B,CAClC,2DAEE,OAAQ,CAER,mBAAoB,CAHpB,iBAAkB,CAElB,UACsB,CACtB,4EAGE,qBAAsB,CADtB,qBAAsB,CADtB,iBAEwB,CACxB,yGAOE,qBAAsB,CACtB,mBAAoB,CAJpB,WAAY,CAHZ,UAAW,CAKX,UAAW,CAJX,iBAAkB,CAClB,UAAW,CAEX,SAGsB,CAC1B,wFAGE,qBAAsB,CADtB,qBAAsB,CADtB,iBAEwB,CAC9B,6BAEE,qBAAsB,CADtB,yBAA0B,CAE1B,UAAW,CACX,eAAiB,CAEjB,gBAAiB,CACjB,qBAAsB,CACtB,uBAAwB,CACxB,wBAAyB,CACzB,mBAAoB,CALpB,kBAKsB,CACtB,wDAGE,kBAAmB,CAFnB,YAAa,CACb,kBAAmB,CAEnB,6BAA8B,CAC9B,gBAAmB,CACnB,8DACE,YAAe,CACnB,yDACE,eAAgB,CAChB,eAAkB,CAClB,oFAIE,qBAAgB,CAChB,6BAA8B,CAC9B,8BAA+B,CAF/B,eAAgB,CAHhB,oBAAqB,CAMrB,cAAe,CALf,WAKiB,CACjB,0FACE,cAAe,CACf,UAAa,CACf,qHACE,eAAkB,CACxB,qDAIE,4BAA8B,CAC9B,4BAA6B,CAC7B,yBAA0B,CAL1B,qBAAsB,CAMtB,eAAgB,CAJhB,eAAgB,CADhB,UAKkB,CAClB,oEAEE,4BAA8B,CAD9B,oBACgC,CAChC,iGACE,YAAe,CACnB,gEAEE,kBAAmB,CADnB,kBACqB,CACzB,uDACE,gBAAmB,CACrB,qDACE,eAAqB,CACvB,kDAGE,UAAW,CAFX,MAAO,CAGP,mBAAoB,CAEpB,iBAAkB,CADlB,mBAAoB,CAHpB,gBAIoB,CACtB,kDAIE,qBAAsB,CACtB,iBAAkB,CAJlB,oBAAqB,CACrB,YAAa,CACb,eAEoB,CACtB,8CACE,YAAe,CACjB,6CAME,6BAAoC,CAFpC,qBAAsB,CACtB,iBAAkB,CAJlB,oBAAqB,CACrB,YAAa,CACb,eAGsC,CACtC,oDACE,UAAa,CACf,sDACE,UAAa,CACf,wCACE,iEAEE,yBAA8B,CAC9B,UAAW,CAFX,cAEa,CAAE,CACvB,wCAEE,oBAAqB,CAErB,gBAAiB,CACjB,iBAAkB,CAJlB,iBAAkB,CAMlB,qBAAsB,CAJtB,SAAU,CAGV,UACwB,CACxB,wCACE,8CACE,gBAAmB,CAAE,CACzB,qDAEE,cAAe,CADf,SACiB,CACrB,uCAME,qBAAsB,CAFtB,WAAY,CACZ,iBAAmB,CAEnB,UAAW,CANX,iBAAkB,CAClB,KAAM,CACN,SAIa,CACf,uCAME,qBAAsB,CAFtB,UAAW,CAFX,MAAO,CAGP,gBAAkB,CAElB,UAAW,CANX,iBAAkB,CAElB,UAIa,CACf,4BAGE,kBAAmB,CAMnB,yBAA8B,CAP9B,YAAa,CAKb,WAAY,CAFZ,MAAO,CAJP,iBAAkB,CASlB,iBAAkB,CANlB,KAAM,CAIN,UAAW,CAFX,WAIoB,CACpB,iDAKE,eAAgB,CADhB,kBAAmB,CAHnB,oBAAqB,CAMrB,cAAe,CADf,eAAiB,CAJjB,aAAc,CACd,iBAIiB,CACjB,2EACE,qBAAsB,CACtB,UAAa,CACf,6EACE,qBAAsB,CACtB,aAAgB,CAExB,eAIE,qBAAsB,CAFtB,qBAAsB,CACtB,eAAgB,CAFhB,iBAGwB,CACxB,kCACE,qBAAwB,CAC1B,wCACE,0CACE,qBAAsB,CACtB,cAAiB,CAAE,CACvB,kCACE,qBAAwB,CAC1B,wCACE,wCACE,qBAAsB,CACtB,cAAiB,CAAE,CACvB,oCAEE,eAAgB,CADhB,qBACkB,CACpB,gCAGE,4BAA6B,CAD7B,yBAA0B,CAE1B,mBAAoB,CAHpB,iBAAkB,CAIlB,UAAa,CACf,oFACE,qBAAsB,CACtB,UAAgB,CAIlB,gMACE,qBAAsB,CACtB,UAAa,CACf,4CAGE,QAAS,CAET,UAAW,CADX,MAAO,CAHP,iBAAkB,CAClB,OAGa,CACb,iDAEE,WAAY,CADZ,KACc,CAChB,wCACE,kDACE,gBAAmB,CAAE,CAC3B,8CAIE,4BAA6B,CAD7B,yBAA0B,CAF1B,qBAAsB,CACtB,WAE+B,CAC/B,oDACE,YAAe,CACjB,oDACE,cAAiB,CACjB,0DACE,iBAAoB,CACpB,wEACE,kBAAqB,CAC7B,+BAKE,2BAA4B,CAF5B,qBAAsB,CAFtB,oBAAqB,CASrB,YAAa,CAFb,eAAgB,CAJhB,WAAY,CAFZ,iBAAkB,CAOlB,sBAAuB,CAHvB,qBAAsB,CACtB,kBAGe,CACf,oDAGE,eAAgB,CADhB,4BAA6B,CAD7B,2BAEkB,CACpB,gDAIE,wBAAyB,CAHzB,oBAAqB,CAErB,MAAO,CADP,eAAgB,CAGhB,UAAa,CACb,sEACE,2BAA8B,CAChC,uEACE,0BAA6B,CACjC,iDACE,qBAAsB,CACtB,YAAa,CACb,SAAY,CACZ,+GAEE,sBAAuB,CADvB,UAAW,CAEX,YAAe,CACnB,yDACE,qBAA2B,CAC3B,+HAEE,sBAAuB,CADvB,UAAW,CAEX,UAAgB,CACpB,oDAEE,kBAAmB,CADnB,mBAAoB,CAEpB,sBAAuB,CACvB,qBAAsB,CACtB,uBAAwB,CACxB,wBAAyB,CACzB,mBAAsB,CACtB,8EACE,SAAY,CACZ,wGAIE,eAAgB,CAFhB,UAAW,CACX,cAAe,CAFf,UAGkB,CACxB,kIACE,qBAAwB,CAC1B,iEACE,oBAAqB,CACrB,SAAY,CACd,2DASE,4BAA6B,CAF7B,6BAA8B,CAC9B,0BAA2B,CAP3B,oBAAqB,CAErB,UAAW,CAGX,gBAAiB,CADjB,eAAgB,CAHhB,qBAAsB,CAEtB,SAK+B,CACjC,4DAGE,kBAAmB,CAOnB,yBAA8B,CAF9B,qBAAsB,CACtB,iBAAkB,CARlB,mBAAoB,CAIpB,WAAY,CAHZ,sBAAuB,CAKvB,gBAAiB,CAIjB,eAAgB,CAPhB,qBAAsB,CAEtB,UAKkB,CAClB,wCACE,kEAEE,yBAA8B,CAD9B,cACgC,CAAE,CACtC,kGAKE,sBAAuB,CAJvB,oBAAqB,CAErB,UAAW,CADX,iBAAkB,CAElB,SACyB,CACzB,wGAOE,eAAgB,CALhB,UAAW,CAGX,UAAW,CAFX,SAAU,CAFV,iBAAkB,CAGlB,OAAQ,CAER,SACkB,CACtB,gGAKE,eAAgB,CAJhB,oBAAqB,CAErB,UAAW,CADX,iBAAkB,CAElB,SACkB,CAClB,sGAOE,eAAgB,CALhB,UAAW,CAGX,UAAW,CAFX,SAAU,CAFV,iBAAkB,CAGlB,OAAQ,CAER,SACkB,CACxB,qEAEE,kBAAmB,CASnB,eAAgB,CADhB,kBAAmB,CAEnB,UAAW,CAXX,mBAAoB,CAapB,eAAgB,CADhB,eAAiB,CALjB,WAAY,CALZ,sBAAuB,CACvB,qBAAsB,CACtB,uBAAwB,CACxB,wBAAyB,CACzB,mBAAoB,CAEpB,UAKkB,CAClB,wCACE,2EAEE,cAAe,CADf,UACiB,CAAE,CACvB,sHACE,eAAkB,CACpB,qHACE,YAAe,CACjB,yEACE,WAAc,CAChB,iHACE,YAAe,CACnB,wDAIE,kBAAmB,CAHnB,oBAAqB,CACrB,WAAY,CACZ,UACqB,CACzB,+BAOE,eAAgB,CALhB,4BAA6B,CAC7B,2BAA4B,CAC5B,yBAA0B,CAH1B,qBAAsB,CAOtB,eAAiB,CAFjB,wBAGiB,CAKjB,wEAKE,eAAgB,CAHhB,iCAAkC,CAClC,kCAAmC,CACnC,yBAA0B,CAH1B,iBAIkB,CACpB,uDACE,iBAAoB,CACtB,uDACE,iBAAoB,CACtB,uDACE,iBAAoB,CACtB,uDACE,iBAAoB,CACtB,uDACE,kBAAqB,CACvB,uDACE,oBAAuB,CACzB,gDAME,mCAAoC,CAEpC,0BAA2B,CAD3B,cAAe,CAFf,gCAAiC,CAJjC,oBAAqB,CAErB,QAAS,CACT,iBAAkB,CAKlB,qBAAsB,CAPtB,OAOwB,CAC1B,oCAEE,UAAW,CADX,gBACa,CAEnB,kBAKE,kBAAmB,CADnB,qBAAsB,CAHtB,qBAAsB,CACtB,YAAa,CACb,kBAEqB,CACrB,sCACE,kBAAqB,CACvB,2CAGE,eAAgB,CADhB,qBAAsB,CADtB,qBAEkB,CAEtB,2BASE,gCAAiC,CALjC,eAAgB,CAChB,qBAAsB,CACtB,mCAAwC,CAHxC,qBAAsB,CADtB,oBAAqB,CAKrB,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CASlB,aAAgB,CAElB,iBAEE,iBAAkB,CADlB,WACoB,CAEtB,mBAGE,iBAAkB,CAClB,eAAgB,CAChB,cAAe,CAJf,yBAA2B,CAC3B,eAAgB,CAIhB,mBAAsB,CAExB,qCAEE,qBAAsB,CACtB,gBAAiB,CAFjB,iBAAkB,CAGlB,gBAAmB,CACnB,kEACE,UAAa,CACf,wCACE,8EAEE,eAAgB,CADhB,cACkB,CAAE,CACxB,iEACE,kBAAqB,CACrB,uEAUE,iBAAkB,CAAlB,kBAAkB,CAAlB,wBAAkB,CAHlB,UAAW,CANX,oBAAqB,CAIrB,UAAW,CAHX,iBAAkB,CAElB,UAAW,CADX,oBAAqB,CASrB,uBAAwB,CADxB,kBAAmB,CALnB,SAM0B,CAEhC,0CACE,yBAA4B,CAE9B,qBAIE,gCAAiC,CAFjC,cAAe,CADf,gBAAiB,CAEjB,eACmC,CACnC,+CAEE,UAAW,CACX,YAAa,CAFb,WAEe,CACf,sDAEE,eAAgB,CADhB,UACkB,CAClB,8DACE,mCAA6C,CACjD,uDACE,sBAAyB,CAC3B,wCACE,qDAGE,eAAgB,CADhB,UAAW,CADX,cAEkB,CAAE,CAC1B,sDAEE,UAAW,CADX,WAAY,CAEZ,iBAAoB,CACtB,gDACE,4BAA6B,CAG7B,UAAW,CACX,eAAiB,CAFjB,mBAEmB,CACrB,mKACE,iBAAoB,CACtB,mKACE,iBAAoB,CACtB,mKACE,iBAAoB,CACtB,mKACE,iBAAoB,CAExB,yBACE,aAAgB,CAElB,yBAEE,aAAc,CADd,kBACgB,CAChB,0DAEE,0BAA2B,CAC3B,oBAAqB,CAFrB,kBAEuB,CACvB,wGAEE,gBAAiB,CADjB,cACmB,CACrB,kGAEE,iBAAkB,CADlB,eACoB,CACtB,uGACE,QAAS,CACT,UAAgB,CACpB,uHAQE,qBAAsB,CACtB,mBAAoB,CAJpB,WAAY,CAJZ,UAAW,CAMX,UAAW,CAJX,SAAU,CADV,iBAAkB,CAElB,UAAc,CAEd,SAGsB,CACxB,wDAEE,0BAA2B,CAD3B,oBAC6B,CAC7B,oFAGE,2BAAkC,CAClC,8BAA+B,CAC/B,mBAAoB,CACpB,2BAA4B,CAJ5B,eAAgB,CADhB,cAK8B,CAChC,qFAEE,eAAgB,CADhB,cACkB,CACpB,+FACE,0BAA6B,CAC/B,gGACE,2BAA8B,CAClC,kFAEE,aAAc,CACd,iBAAkB,CAFlB,SAEoB,CACtB,mEACE,kBAAqB,CAEzB,4BAGE,QAAS,CACT,MAAO,CAHP,iBAAkB,CAIlB,OAAQ,CAHR,KAAM,CAIN,aAAgB,CAElB,uEACE,sBAA0B,CAE5B,uBACE,wBAA2B,CAC3B,mDASE,4BAA6B,CAF7B,6BAA8B,CAC9B,0BAA2B,CAP3B,oBAAqB,CAErB,UAAW,CAGX,gBAAiB,CADjB,eAAgB,CAHhB,qBAAsB,CAEtB,SAK+B,CACjC,oDAOE,eAAgB,CALhB,4BAA6B,CAC7B,2BAA4B,CAC5B,yBAA0B,CAH1B,qBAAsB,CAOtB,eAAiB,CACjB,cAAe,CAHf,wBAGiB,CACjB,wCACE,0DAEE,+BAAoC,CADpC,cACsC,CAAE,CAC5C,6FAKE,eAAgB,CAHhB,iCAAkC,CAClC,kCAAmC,CACnC,yBAA0B,CAH1B,iBAIkB,CACpB,+EACE,2BAA+B,CACjC,+EACE,2BAA+B,CACjC,+EACE,2BAA+B,CACjC,+EACE,2BAA+B,CACjC,+EACE,4BAAgC,CAClC,4EACE,oBAAuB,CACzB,qEAME,mCAAoC,CAEpC,0BAA2B,CAD3B,cAAe,CAFf,gCAAiC,CAJjC,oBAAqB,CAErB,QAAS,CACT,iBAAkB,CAKlB,qBAAsB,CAPtB,OAOwB,CAC1B,yDAEE,UAAW,CADX,gBACa,CACjB,oDAGE,kBAAmB,CAOnB,yBAA8B,CAF9B,qBAAsB,CACtB,iBAAkB,CARlB,mBAAoB,CAIpB,WAAY,CAHZ,sBAAuB,CAKvB,gBAAiB,CAIjB,eAAgB,CAPhB,qBAAsB,CAEtB,UAKkB,CAClB,wCACE,0DAEE,yBAA8B,CAD9B,cACgC,CAAE,CACtC,0FAKE,sBAAuB,CAJvB,oBAAqB,CAErB,UAAW,CADX,iBAAkB,CAElB,SACyB,CACzB,gGAOE,eAAgB,CALhB,UAAW,CAGX,UAAW,CAFX,SAAU,CAFV,iBAAkB,CAGlB,OAAQ,CAER,SACkB,CACtB,wFAKE,eAAgB,CAJhB,oBAAqB,CAErB,UAAW,CADX,iBAAkB,CAElB,SACkB,CAClB,8FAOE,eAAgB,CALhB,UAAW,CAGX,UAAW,CAFX,SAAU,CAFV,iBAAkB,CAGlB,OAAQ,CAER,SACkB,CAE1B,WACE,qBAAwB,CACxB,4CACE,qBAAwB,CACxB,gHACE,UAAa,CACf,uJAEE,eAAgB,CADhB,qBAAsB,CAEtB,UAAa,CAGf,yHACE,4BAAgC,CAGlC,yHACE,4BAAgC,CACpC,oFAEE,6BAAoC,CADpC,iBACsC,CACtC,qHACE,yBAA8B,CAC9B,UAAa,CAGjB,6GACE,UAAa,CACf,6CACE,UAAW,CACX,mBAAoB,CAEpB,iBAAkB,CADlB,mBACoB,CAExB,+BAEE,UAAW,CADX,cACa,CACb,wCACE,qCAEE,+BAAoC,CADpC,cACsC,CAAE,CAC5C,oCACE,UAAa,CAEjB,kBAEE,eAAgB,CADhB,iBACkB,CAClB,2CAEE,kBAAmB,CADnB,iBACqB,CAEzB,4BACE,eAAkB,CAClB,6DACE,UAAa,CACb,oEAEE,eAAgB,CADhB,UACkB,CAClB,4EACE,mCAA6C,CACjD,qEACE,sBAAyB,CAC3B,wCACE,mEAEE,eAAgB,CADhB,UACkB,CAAE,CAE5B,oDACE,UAAa", "file": "tabulator_midnight.min.css", "sourcesContent": [".tabulator {\n  position: relative;\n  border: 1px solid #333;\n  background-color: #222;\n  font-size: 14px;\n  text-align: left;\n  overflow: hidden;\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0); }\n  .tabulator[tabulator-layout=\"fitDataFill\"] .tabulator-tableholder .tabulator-table {\n    min-width: 100%; }\n  .tabulator[tabulator-layout=\"fitDataTable\"] {\n    display: inline-block; }\n  .tabulator.tabulator-block-select {\n    user-select: none; }\n  .tabulator.tabulator-ranges .tabulator-cell:not(.tabulator-editing) {\n    user-select: none; }\n  .tabulator .tabulator-header {\n    position: relative;\n    box-sizing: border-box;\n    width: 100%;\n    border-bottom: 1px solid #999;\n    background-color: #333;\n    color: #fff;\n    font-weight: bold;\n    white-space: nowrap;\n    overflow: hidden;\n    -moz-user-select: none;\n    -khtml-user-select: none;\n    -webkit-user-select: none;\n    -o-user-select: none;\n    outline: none; }\n    .tabulator .tabulator-header.tabulator-header-hidden {\n      display: none; }\n    .tabulator .tabulator-header .tabulator-header-contents {\n      position: relative;\n      overflow: hidden; }\n      .tabulator .tabulator-header .tabulator-header-contents .tabulator-headers {\n        display: inline-block; }\n    .tabulator .tabulator-header .tabulator-col {\n      display: inline-flex;\n      position: relative;\n      box-sizing: border-box;\n      flex-direction: column;\n      justify-content: flex-start;\n      border-right: 1px solid #aaa;\n      background: #333;\n      text-align: left;\n      vertical-align: bottom;\n      overflow: hidden; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-moving {\n        position: absolute;\n        border: 1px solid #999;\n        background: #1a1a1a;\n        pointer-events: none; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-range-highlight {\n        background-color: #999;\n        color: #000000; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-range-selected {\n        background-color: #ccc;\n        color: #333; }\n      .tabulator .tabulator-header .tabulator-col .tabulator-col-content {\n        box-sizing: border-box;\n        position: relative;\n        padding: 4px; }\n        .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-header-popup-button {\n          padding: 0 8px; }\n          .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-header-popup-button:hover {\n            cursor: pointer;\n            opacity: .6; }\n        .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title-holder {\n          position: relative; }\n        .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title {\n          box-sizing: border-box;\n          width: 100%;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          vertical-align: bottom; }\n          .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title.tabulator-col-title-wrap {\n            white-space: normal;\n            text-overflow: initial; }\n          .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title .tabulator-title-editor {\n            box-sizing: border-box;\n            width: 100%;\n            border: 1px solid #999;\n            padding: 1px;\n            background: #fff; }\n          .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title .tabulator-header-popup-button + .tabulator-title-editor {\n            width: calc(100% - 22px); }\n        .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-sorter {\n          display: flex;\n          align-items: center;\n          position: absolute;\n          top: 0;\n          bottom: 0;\n          right: 4px; }\n          .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-sorter .tabulator-arrow {\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-bottom: 6px solid #bbb; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-col-group .tabulator-col-group-cols {\n        position: relative;\n        display: flex;\n        border-top: 1px solid #aaa;\n        overflow: hidden;\n        margin-right: -1px; }\n      .tabulator .tabulator-header .tabulator-col .tabulator-header-filter {\n        position: relative;\n        box-sizing: border-box;\n        margin-top: 2px;\n        width: 100%;\n        text-align: center; }\n        .tabulator .tabulator-header .tabulator-col .tabulator-header-filter textarea {\n          height: auto !important; }\n        .tabulator .tabulator-header .tabulator-col .tabulator-header-filter svg {\n          margin-top: 3px; }\n        .tabulator .tabulator-header .tabulator-col .tabulator-header-filter input::-ms-clear {\n          width: 0;\n          height: 0; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-sortable .tabulator-col-title {\n        padding-right: 25px; }\n      @media (hover: hover) and (pointer: fine) {\n        .tabulator .tabulator-header .tabulator-col.tabulator-sortable.tabulator-col-sorter-element:hover {\n          cursor: pointer;\n          background-color: #1a1a1a; } }\n      .tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=\"none\"] .tabulator-col-content .tabulator-col-sorter {\n        color: #bbb; }\n        @media (hover: hover) and (pointer: fine) {\n          .tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=\"none\"] .tabulator-col-content .tabulator-col-sorter.tabulator-col-sorter-element .tabulator-arrow:hover {\n            cursor: pointer;\n            border-bottom: 6px solid #555; } }\n        .tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=\"none\"] .tabulator-col-content .tabulator-col-sorter .tabulator-arrow {\n          border-top: none;\n          border-bottom: 6px solid #bbb; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=\"ascending\"] .tabulator-col-content .tabulator-col-sorter {\n        color: #666; }\n        @media (hover: hover) and (pointer: fine) {\n          .tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=\"ascending\"] .tabulator-col-content .tabulator-col-sorter.tabulator-col-sorter-element .tabulator-arrow:hover {\n            cursor: pointer;\n            border-bottom: 6px solid #555; } }\n        .tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=\"ascending\"] .tabulator-col-content .tabulator-col-sorter .tabulator-arrow {\n          border-top: none;\n          border-bottom: 6px solid #666; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=\"descending\"] .tabulator-col-content .tabulator-col-sorter {\n        color: #666; }\n        @media (hover: hover) and (pointer: fine) {\n          .tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=\"descending\"] .tabulator-col-content .tabulator-col-sorter.tabulator-col-sorter-element .tabulator-arrow:hover {\n            cursor: pointer;\n            border-top: 6px solid #555; } }\n        .tabulator .tabulator-header .tabulator-col.tabulator-sortable[aria-sort=\"descending\"] .tabulator-col-content .tabulator-col-sorter .tabulator-arrow {\n          border-bottom: none;\n          border-top: 6px solid #666;\n          color: #666; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-col-vertical .tabulator-col-content .tabulator-col-title {\n        writing-mode: vertical-rl;\n        text-orientation: mixed;\n        display: flex;\n        align-items: center;\n        justify-content: center; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-col-vertical.tabulator-col-vertical-flip .tabulator-col-title {\n        transform: rotate(180deg); }\n      .tabulator .tabulator-header .tabulator-col.tabulator-col-vertical.tabulator-sortable .tabulator-col-title {\n        padding-right: 0;\n        padding-top: 20px; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-col-vertical.tabulator-sortable.tabulator-col-vertical-flip .tabulator-col-title {\n        padding-right: 0;\n        padding-bottom: 20px; }\n      .tabulator .tabulator-header .tabulator-col.tabulator-col-vertical.tabulator-sortable .tabulator-col-sorter {\n        justify-content: center;\n        left: 0;\n        right: 0;\n        top: 4px;\n        bottom: auto; }\n    .tabulator .tabulator-header .tabulator-frozen {\n      position: sticky;\n      left: 0;\n      z-index: 11; }\n      .tabulator .tabulator-header .tabulator-frozen.tabulator-frozen-left {\n        border-right: 2px solid #888; }\n      .tabulator .tabulator-header .tabulator-frozen.tabulator-frozen-right {\n        border-left: 2px solid #888; }\n    .tabulator .tabulator-header .tabulator-calcs-holder {\n      box-sizing: border-box;\n      display: inline-block;\n      background: #404040 !important;\n      border-top: 1px solid #888;\n      border-bottom: 1px solid #aaa; }\n      .tabulator .tabulator-header .tabulator-calcs-holder .tabulator-row {\n        background: #404040 !important; }\n        .tabulator .tabulator-header .tabulator-calcs-holder .tabulator-row .tabulator-col-resize-handle {\n          display: none; }\n    .tabulator .tabulator-header .tabulator-frozen-rows-holder {\n      display: inline-block; }\n      .tabulator .tabulator-header .tabulator-frozen-rows-holder:empty {\n        display: none; }\n  .tabulator .tabulator-tableholder {\n    position: relative;\n    width: 100%;\n    white-space: nowrap;\n    overflow: auto;\n    -webkit-overflow-scrolling: touch; }\n    .tabulator .tabulator-tableholder:focus {\n      outline: none; }\n    .tabulator .tabulator-tableholder .tabulator-placeholder {\n      box-sizing: border-box;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      min-width: 100%;\n      width: 100%; }\n      .tabulator .tabulator-tableholder .tabulator-placeholder[tabulator-render-mode=\"virtual\"] {\n        min-height: 100%; }\n      .tabulator .tabulator-tableholder .tabulator-placeholder .tabulator-placeholder-contents {\n        display: inline-block;\n        text-align: center;\n        padding: 10px;\n        color: #ccc;\n        font-weight: bold;\n        font-size: 20px;\n        white-space: normal; }\n    .tabulator .tabulator-tableholder .tabulator-table {\n      position: relative;\n      display: inline-block;\n      background-color: #666;\n      white-space: nowrap;\n      overflow: visible;\n      color: #fff; }\n      .tabulator .tabulator-tableholder .tabulator-table .tabulator-row.tabulator-calcs {\n        font-weight: bold;\n        background: #373737 !important; }\n        .tabulator .tabulator-tableholder .tabulator-table .tabulator-row.tabulator-calcs.tabulator-calcs-top {\n          border-bottom: 2px solid #888; }\n        .tabulator .tabulator-tableholder .tabulator-table .tabulator-row.tabulator-calcs.tabulator-calcs-bottom {\n          border-top: 2px solid #888; }\n    .tabulator .tabulator-tableholder .tabulator-range-overlay {\n      position: absolute;\n      inset: 0;\n      z-index: 10;\n      pointer-events: none; }\n      .tabulator .tabulator-tableholder .tabulator-range-overlay .tabulator-range {\n        position: absolute;\n        box-sizing: border-box;\n        border: 1px solid #ccc; }\n        .tabulator .tabulator-tableholder .tabulator-range-overlay .tabulator-range.tabulator-range-active::after {\n          content: '';\n          position: absolute;\n          right: -3px;\n          bottom: -3px;\n          width: 6px;\n          height: 6px;\n          background-color: #ccc;\n          border-radius: 999px; }\n      .tabulator .tabulator-tableholder .tabulator-range-overlay .tabulator-range-cell-active {\n        position: absolute;\n        box-sizing: border-box;\n        border: 2px solid #ccc; }\n  .tabulator .tabulator-footer {\n    border-top: 1px solid #999;\n    background-color: #333;\n    color: #333;\n    font-weight: bold;\n    white-space: nowrap;\n    user-select: none;\n    -moz-user-select: none;\n    -khtml-user-select: none;\n    -webkit-user-select: none;\n    -o-user-select: none; }\n    .tabulator .tabulator-footer .tabulator-footer-contents {\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      justify-content: space-between;\n      padding: 5px 10px; }\n      .tabulator .tabulator-footer .tabulator-footer-contents:empty {\n        display: none; }\n    .tabulator .tabulator-footer .tabulator-spreadsheet-tabs {\n      margin-top: -5px;\n      overflow-x: auto; }\n      .tabulator .tabulator-footer .tabulator-spreadsheet-tabs .tabulator-spreadsheet-tab {\n        display: inline-block;\n        padding: 5px;\n        border: #333 1px solid;\n        border-top: none;\n        border-bottom-left-radius: 5px;\n        border-bottom-right-radius: 5px;\n        font-size: .9em; }\n        .tabulator .tabulator-footer .tabulator-spreadsheet-tabs .tabulator-spreadsheet-tab:hover {\n          cursor: pointer;\n          opacity: .7; }\n        .tabulator .tabulator-footer .tabulator-spreadsheet-tabs .tabulator-spreadsheet-tab.tabulator-spreadsheet-tab-active {\n          background: #fff; }\n    .tabulator .tabulator-footer .tabulator-calcs-holder {\n      box-sizing: border-box;\n      width: 100%;\n      text-align: left;\n      background: #404040 !important;\n      border-bottom: 1px solid #888;\n      border-top: 1px solid #888;\n      overflow: hidden; }\n      .tabulator .tabulator-footer .tabulator-calcs-holder .tabulator-row {\n        display: inline-block;\n        background: #404040 !important; }\n        .tabulator .tabulator-footer .tabulator-calcs-holder .tabulator-row .tabulator-col-resize-handle {\n          display: none; }\n      .tabulator .tabulator-footer .tabulator-calcs-holder:only-child {\n        margin-bottom: -5px;\n        border-bottom: none; }\n    .tabulator .tabulator-footer > * + .tabulator-page-counter {\n      margin-left: 10px; }\n    .tabulator .tabulator-footer .tabulator-page-counter {\n      font-weight: normal; }\n    .tabulator .tabulator-footer .tabulator-paginator {\n      flex: 1;\n      text-align: right;\n      color: #333;\n      font-family: inherit;\n      font-weight: inherit;\n      font-size: inherit; }\n    .tabulator .tabulator-footer .tabulator-page-size {\n      display: inline-block;\n      margin: 0 5px;\n      padding: 2px 5px;\n      border: 1px solid #aaa;\n      border-radius: 3px; }\n    .tabulator .tabulator-footer .tabulator-pages {\n      margin: 0 7px; }\n    .tabulator .tabulator-footer .tabulator-page {\n      display: inline-block;\n      margin: 0 2px;\n      padding: 2px 5px;\n      border: 1px solid #aaa;\n      border-radius: 3px;\n      background: rgba(255, 255, 255, 0.2); }\n      .tabulator .tabulator-footer .tabulator-page.active {\n        color: #fff; }\n      .tabulator .tabulator-footer .tabulator-page:disabled {\n        opacity: .5; }\n      @media (hover: hover) and (pointer: fine) {\n        .tabulator .tabulator-footer .tabulator-page:not(disabled):hover {\n          cursor: pointer;\n          background: rgba(0, 0, 0, 0.2);\n          color: #fff; } }\n  .tabulator .tabulator-col-resize-handle {\n    position: relative;\n    display: inline-block;\n    width: 6px;\n    margin-left: -3px;\n    margin-right: -3px;\n    z-index: 11;\n    vertical-align: middle; }\n    @media (hover: hover) and (pointer: fine) {\n      .tabulator .tabulator-col-resize-handle:hover {\n        cursor: ew-resize; } }\n    .tabulator .tabulator-col-resize-handle:last-of-type {\n      width: 3px;\n      margin-right: 0; }\n  .tabulator .tabulator-col-resize-guide {\n    position: absolute;\n    top: 0;\n    width: 4px;\n    height: 100%;\n    margin-left: -0.5px;\n    background-color: #999;\n    opacity: .5; }\n  .tabulator .tabulator-row-resize-guide {\n    position: absolute;\n    left: 0;\n    width: 100%;\n    height: 4px;\n    margin-top: -0.5px;\n    background-color: #999;\n    opacity: .5; }\n  .tabulator .tabulator-alert {\n    position: absolute;\n    display: flex;\n    align-items: center;\n    top: 0;\n    left: 0;\n    z-index: 100;\n    height: 100%;\n    width: 100%;\n    background: rgba(0, 0, 0, 0.4);\n    text-align: center; }\n    .tabulator .tabulator-alert .tabulator-alert-msg {\n      display: inline-block;\n      margin: 0 auto;\n      padding: 10px 20px;\n      border-radius: 10px;\n      background: #fff;\n      font-weight: bold;\n      font-size: 16px; }\n      .tabulator .tabulator-alert .tabulator-alert-msg.tabulator-alert-state-msg {\n        border: 4px solid #333;\n        color: #000; }\n      .tabulator .tabulator-alert .tabulator-alert-msg.tabulator-alert-state-error {\n        border: 4px solid #D00;\n        color: #590000; }\n\n.tabulator-row {\n  position: relative;\n  box-sizing: border-box;\n  min-height: 22px;\n  background-color: #666; }\n  .tabulator-row.tabulator-row-even {\n    background-color: #444; }\n  @media (hover: hover) and (pointer: fine) {\n    .tabulator-row.tabulator-selectable:hover {\n      background-color: #999;\n      cursor: pointer; } }\n  .tabulator-row.tabulator-selected {\n    background-color: #000; }\n  @media (hover: hover) and (pointer: fine) {\n    .tabulator-row.tabulator-selected:hover {\n      background-color: #888;\n      cursor: pointer; } }\n  .tabulator-row.tabulator-row-moving {\n    border: 1px solid #000;\n    background: #fff; }\n  .tabulator-row.tabulator-moving {\n    position: absolute;\n    border-top: 1px solid #888;\n    border-bottom: 1px solid #888;\n    pointer-events: none;\n    z-index: 15; }\n  .tabulator-row.tabulator-range-highlight .tabulator-cell.tabulator-range-row-header {\n    background-color: #999;\n    color: #000000; }\n  .tabulator-row.tabulator-range-highlight.tabulator-range-selected .tabulator-cell.tabulator-range-row-header {\n    background-color: #ccc;\n    color: #333; }\n  .tabulator-row.tabulator-range-selected .tabulator-cell.tabulator-range-row-header {\n    background-color: #ccc;\n    color: #333; }\n  .tabulator-row .tabulator-row-resize-handle {\n    position: absolute;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    height: 5px; }\n    .tabulator-row .tabulator-row-resize-handle.prev {\n      top: 0;\n      bottom: auto; }\n    @media (hover: hover) and (pointer: fine) {\n      .tabulator-row .tabulator-row-resize-handle:hover {\n        cursor: ns-resize; } }\n  .tabulator-row .tabulator-responsive-collapse {\n    box-sizing: border-box;\n    padding: 5px;\n    border-top: 1px solid #888;\n    border-bottom: 1px solid #888; }\n    .tabulator-row .tabulator-responsive-collapse:empty {\n      display: none; }\n    .tabulator-row .tabulator-responsive-collapse table {\n      font-size: 14px; }\n      .tabulator-row .tabulator-responsive-collapse table tr td {\n        position: relative; }\n        .tabulator-row .tabulator-responsive-collapse table tr td:first-of-type {\n          padding-right: 10px; }\n  .tabulator-row .tabulator-cell {\n    display: inline-block;\n    position: relative;\n    box-sizing: border-box;\n    padding: 4px;\n    border-right: 1px solid #888;\n    vertical-align: middle;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    outline: none; }\n    .tabulator-row .tabulator-cell.tabulator-row-header {\n      border-right: 1px solid #333;\n      border-bottom: 1px solid #888;\n      background: #333; }\n    .tabulator-row .tabulator-cell.tabulator-frozen {\n      display: inline-block;\n      position: sticky;\n      left: 0;\n      background-color: inherit;\n      z-index: 11; }\n      .tabulator-row .tabulator-cell.tabulator-frozen.tabulator-frozen-left {\n        border-right: 2px solid #888; }\n      .tabulator-row .tabulator-cell.tabulator-frozen.tabulator-frozen-right {\n        border-left: 2px solid #888; }\n    .tabulator-row .tabulator-cell.tabulator-editing {\n      border: 1px solid #999;\n      outline: none;\n      padding: 0; }\n      .tabulator-row .tabulator-cell.tabulator-editing input, .tabulator-row .tabulator-cell.tabulator-editing select {\n        border: 1px;\n        background: transparent;\n        outline: none; }\n    .tabulator-row .tabulator-cell.tabulator-validation-fail {\n      border: 1px solid #dd0000; }\n      .tabulator-row .tabulator-cell.tabulator-validation-fail input, .tabulator-row .tabulator-cell.tabulator-validation-fail select {\n        border: 1px;\n        background: transparent;\n        color: #dd0000; }\n    .tabulator-row .tabulator-cell.tabulator-row-handle {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      -moz-user-select: none;\n      -khtml-user-select: none;\n      -webkit-user-select: none;\n      -o-user-select: none; }\n      .tabulator-row .tabulator-cell.tabulator-row-handle .tabulator-row-handle-box {\n        width: 80%; }\n        .tabulator-row .tabulator-cell.tabulator-row-handle .tabulator-row-handle-box .tabulator-row-handle-bar {\n          width: 100%;\n          height: 3px;\n          margin-top: 2px;\n          background: #666; }\n    .tabulator-row .tabulator-cell.tabulator-range-selected:not(.tabulator-range-only-cell-selected):not(.tabulator-range-row-header) {\n      background-color: #000; }\n    .tabulator-row .tabulator-cell .tabulator-data-tree-branch-empty {\n      display: inline-block;\n      width: 7px; }\n    .tabulator-row .tabulator-cell .tabulator-data-tree-branch {\n      display: inline-block;\n      vertical-align: middle;\n      height: 9px;\n      width: 7px;\n      margin-top: -9px;\n      margin-right: 5px;\n      border-bottom-left-radius: 1px;\n      border-left: 2px solid #888;\n      border-bottom: 2px solid #888; }\n    .tabulator-row .tabulator-cell .tabulator-data-tree-control {\n      display: inline-flex;\n      justify-content: center;\n      align-items: center;\n      vertical-align: middle;\n      height: 11px;\n      width: 11px;\n      margin-right: 5px;\n      border: 1px solid #fff;\n      border-radius: 2px;\n      background: rgba(0, 0, 0, 0.1);\n      overflow: hidden; }\n      @media (hover: hover) and (pointer: fine) {\n        .tabulator-row .tabulator-cell .tabulator-data-tree-control:hover {\n          cursor: pointer;\n          background: rgba(0, 0, 0, 0.2); } }\n      .tabulator-row .tabulator-cell .tabulator-data-tree-control .tabulator-data-tree-control-collapse {\n        display: inline-block;\n        position: relative;\n        height: 7px;\n        width: 1px;\n        background: transparent; }\n        .tabulator-row .tabulator-cell .tabulator-data-tree-control .tabulator-data-tree-control-collapse:after {\n          position: absolute;\n          content: \"\";\n          left: -3px;\n          top: 3px;\n          height: 1px;\n          width: 7px;\n          background: #fff; }\n      .tabulator-row .tabulator-cell .tabulator-data-tree-control .tabulator-data-tree-control-expand {\n        display: inline-block;\n        position: relative;\n        height: 7px;\n        width: 1px;\n        background: #fff; }\n        .tabulator-row .tabulator-cell .tabulator-data-tree-control .tabulator-data-tree-control-expand:after {\n          position: absolute;\n          content: \"\";\n          left: -3px;\n          top: 3px;\n          height: 1px;\n          width: 7px;\n          background: #fff; }\n    .tabulator-row .tabulator-cell .tabulator-responsive-collapse-toggle {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      -moz-user-select: none;\n      -khtml-user-select: none;\n      -webkit-user-select: none;\n      -o-user-select: none;\n      height: 15px;\n      width: 15px;\n      border-radius: 20px;\n      background: #666;\n      color: #666;\n      font-weight: bold;\n      font-size: 1.1em; }\n      @media (hover: hover) and (pointer: fine) {\n        .tabulator-row .tabulator-cell .tabulator-responsive-collapse-toggle:hover {\n          opacity: .7;\n          cursor: pointer; } }\n      .tabulator-row .tabulator-cell .tabulator-responsive-collapse-toggle.open .tabulator-responsive-collapse-toggle-close {\n        display: initial; }\n      .tabulator-row .tabulator-cell .tabulator-responsive-collapse-toggle.open .tabulator-responsive-collapse-toggle-open {\n        display: none; }\n      .tabulator-row .tabulator-cell .tabulator-responsive-collapse-toggle svg {\n        stroke: #666; }\n      .tabulator-row .tabulator-cell .tabulator-responsive-collapse-toggle .tabulator-responsive-collapse-toggle-close {\n        display: none; }\n    .tabulator-row .tabulator-cell .tabulator-traffic-light {\n      display: inline-block;\n      height: 14px;\n      width: 14px;\n      border-radius: 14px; }\n  .tabulator-row.tabulator-group {\n    box-sizing: border-box;\n    border-bottom: 1px solid #999;\n    border-right: 1px solid #888;\n    border-top: 1px solid #999;\n    padding: 5px;\n    padding-left: 10px;\n    background: #ccc;\n    font-weight: bold;\n    min-width: 100%; }\n    @media (hover: hover) and (pointer: fine) {\n      .tabulator-row.tabulator-group:hover {\n        cursor: pointer;\n        background-color: rgba(0, 0, 0, 0.1); } }\n    .tabulator-row.tabulator-group.tabulator-group-visible .tabulator-arrow {\n      margin-right: 10px;\n      border-left: 6px solid transparent;\n      border-right: 6px solid transparent;\n      border-top: 6px solid #666;\n      border-bottom: 0; }\n    .tabulator-row.tabulator-group.tabulator-group-level-1 {\n      padding-left: 30px; }\n    .tabulator-row.tabulator-group.tabulator-group-level-2 {\n      padding-left: 50px; }\n    .tabulator-row.tabulator-group.tabulator-group-level-3 {\n      padding-left: 70px; }\n    .tabulator-row.tabulator-group.tabulator-group-level-4 {\n      padding-left: 90px; }\n    .tabulator-row.tabulator-group.tabulator-group-level-5 {\n      padding-left: 110px; }\n    .tabulator-row.tabulator-group .tabulator-group-toggle {\n      display: inline-block; }\n    .tabulator-row.tabulator-group .tabulator-arrow {\n      display: inline-block;\n      width: 0;\n      height: 0;\n      margin-right: 16px;\n      border-top: 6px solid transparent;\n      border-bottom: 6px solid transparent;\n      border-right: 0;\n      border-left: 6px solid #666;\n      vertical-align: middle; }\n    .tabulator-row.tabulator-group span {\n      margin-left: 10px;\n      color: #d00; }\n\n.tabulator-toggle {\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: row;\n  border: 1px solid #ccc;\n  background: #dcdcdc; }\n  .tabulator-toggle.tabulator-toggle-on {\n    background: #1c6cc2; }\n  .tabulator-toggle .tabulator-toggle-switch {\n    box-sizing: border-box;\n    border: 1px solid #ccc;\n    background: #fff; }\n\n.tabulator-popup-container {\n  position: absolute;\n  display: inline-block;\n  box-sizing: border-box;\n  background: #666;\n  border: 1px solid #888;\n  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.2);\n  font-size: 14px;\n  overflow-y: auto;\n  -webkit-overflow-scrolling: touch;\n  z-index: 10000; }\n\n.tabulator-popup {\n  padding: 5px;\n  border-radius: 3px; }\n\n.tabulator-tooltip {\n  max-width: Min(500px, 100%);\n  padding: 3px 5px;\n  border-radius: 2px;\n  box-shadow: none;\n  font-size: 12px;\n  pointer-events: none; }\n\n.tabulator-menu .tabulator-menu-item {\n  position: relative;\n  box-sizing: border-box;\n  padding: 5px 10px;\n  user-select: none; }\n  .tabulator-menu .tabulator-menu-item.tabulator-menu-item-disabled {\n    opacity: .5; }\n  @media (hover: hover) and (pointer: fine) {\n    .tabulator-menu .tabulator-menu-item:not(.tabulator-menu-item-disabled):hover {\n      cursor: pointer;\n      background: #444; } }\n  .tabulator-menu .tabulator-menu-item.tabulator-menu-item-submenu {\n    padding-right: 25px; }\n    .tabulator-menu .tabulator-menu-item.tabulator-menu-item-submenu::after {\n      display: inline-block;\n      position: absolute;\n      top: calc(5px + .4em);\n      right: 10px;\n      height: 7px;\n      width: 7px;\n      content: '';\n      border-width: 1px 1px 0 0;\n      border-style: solid;\n      border-color: #888;\n      vertical-align: top;\n      transform: rotate(45deg); }\n\n.tabulator-menu .tabulator-menu-separator {\n  border-top: 1px solid #888; }\n\n.tabulator-edit-list {\n  max-height: 200px;\n  font-size: 14px;\n  overflow-y: auto;\n  -webkit-overflow-scrolling: touch; }\n  .tabulator-edit-list .tabulator-edit-list-item {\n    padding: 4px;\n    color: #fff;\n    outline: none; }\n    .tabulator-edit-list .tabulator-edit-list-item.active {\n      color: #666;\n      background: #999; }\n      .tabulator-edit-list .tabulator-edit-list-item.active.focused {\n        outline: 1px solid rgba(102, 102, 102, 0.5); }\n    .tabulator-edit-list .tabulator-edit-list-item.focused {\n      outline: 1px solid #999; }\n    @media (hover: hover) and (pointer: fine) {\n      .tabulator-edit-list .tabulator-edit-list-item:hover {\n        cursor: pointer;\n        color: #666;\n        background: #999; } }\n  .tabulator-edit-list .tabulator-edit-list-placeholder {\n    padding: 4px;\n    color: #fff;\n    text-align: center; }\n  .tabulator-edit-list .tabulator-edit-list-group {\n    border-bottom: 1px solid #888;\n    padding: 4px;\n    padding-top: 6px;\n    color: #fff;\n    font-weight: bold; }\n  .tabulator-edit-list .tabulator-edit-list-item.tabulator-edit-list-group-level-2, .tabulator-edit-list .tabulator-edit-list-group.tabulator-edit-list-group-level-2 {\n    padding-left: 12px; }\n  .tabulator-edit-list .tabulator-edit-list-item.tabulator-edit-list-group-level-3, .tabulator-edit-list .tabulator-edit-list-group.tabulator-edit-list-group-level-3 {\n    padding-left: 20px; }\n  .tabulator-edit-list .tabulator-edit-list-item.tabulator-edit-list-group-level-4, .tabulator-edit-list .tabulator-edit-list-group.tabulator-edit-list-group-level-4 {\n    padding-left: 28px; }\n  .tabulator-edit-list .tabulator-edit-list-item.tabulator-edit-list-group-level-5, .tabulator-edit-list .tabulator-edit-list-group.tabulator-edit-list-group-level-5 {\n    padding-left: 36px; }\n\n.tabulator.tabulator-ltr {\n  direction: ltr; }\n\n.tabulator.tabulator-rtl {\n  text-align: initial;\n  direction: rtl; }\n  .tabulator.tabulator-rtl .tabulator-header .tabulator-col {\n    text-align: initial;\n    border-left: 1px solid #aaa;\n    border-right: initial; }\n    .tabulator.tabulator-rtl .tabulator-header .tabulator-col.tabulator-col-group .tabulator-col-group-cols {\n      margin-right: initial;\n      margin-left: -1px; }\n    .tabulator.tabulator-rtl .tabulator-header .tabulator-col.tabulator-sortable .tabulator-col-title {\n      padding-right: 0;\n      padding-left: 25px; }\n    .tabulator.tabulator-rtl .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-sorter {\n      left: 8px;\n      right: initial; }\n  .tabulator.tabulator-rtl .tabulator-tableholder .tabulator-range-overlay .tabulator-range.tabulator-range-active::after {\n    content: '';\n    position: absolute;\n    left: -3px;\n    right: initial;\n    bottom: -3px;\n    width: 6px;\n    height: 6px;\n    background-color: #ccc;\n    border-radius: 999px; }\n  .tabulator.tabulator-rtl .tabulator-row .tabulator-cell {\n    border-right: initial;\n    border-left: 1px solid #888; }\n    .tabulator.tabulator-rtl .tabulator-row .tabulator-cell .tabulator-data-tree-branch {\n      margin-right: initial;\n      margin-left: 5px;\n      border-bottom-left-radius: initial;\n      border-bottom-right-radius: 1px;\n      border-left: initial;\n      border-right: 2px solid #888; }\n    .tabulator.tabulator-rtl .tabulator-row .tabulator-cell .tabulator-data-tree-control {\n      margin-right: initial;\n      margin-left: 5px; }\n    .tabulator.tabulator-rtl .tabulator-row .tabulator-cell.tabulator-frozen.tabulator-frozen-left {\n      border-left: 2px solid #888; }\n    .tabulator.tabulator-rtl .tabulator-row .tabulator-cell.tabulator-frozen.tabulator-frozen-right {\n      border-right: 2px solid #888; }\n  .tabulator.tabulator-rtl .tabulator-row .tabulator-col-resize-handle:last-of-type {\n    width: 3px;\n    margin-left: 0;\n    margin-right: -3px; }\n  .tabulator.tabulator-rtl .tabulator-footer .tabulator-calcs-holder {\n    text-align: initial; }\n\n.tabulator-print-fullscreen {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 10000; }\n\nbody.tabulator-print-fullscreen-hide > *:not(.tabulator-print-fullscreen) {\n  display: none !important; }\n\n.tabulator-print-table {\n  border-collapse: collapse; }\n  .tabulator-print-table .tabulator-data-tree-branch {\n    display: inline-block;\n    vertical-align: middle;\n    height: 9px;\n    width: 7px;\n    margin-top: -9px;\n    margin-right: 5px;\n    border-bottom-left-radius: 1px;\n    border-left: 2px solid #888;\n    border-bottom: 2px solid #888; }\n  .tabulator-print-table .tabulator-print-table-group {\n    box-sizing: border-box;\n    border-bottom: 1px solid #999;\n    border-right: 1px solid #888;\n    border-top: 1px solid #999;\n    padding: 5px;\n    padding-left: 10px;\n    background: #ccc;\n    font-weight: bold;\n    min-width: 100%; }\n    @media (hover: hover) and (pointer: fine) {\n      .tabulator-print-table .tabulator-print-table-group:hover {\n        cursor: pointer;\n        background-color: rgba(0, 0, 0, 0.1); } }\n    .tabulator-print-table .tabulator-print-table-group.tabulator-group-visible .tabulator-arrow {\n      margin-right: 10px;\n      border-left: 6px solid transparent;\n      border-right: 6px solid transparent;\n      border-top: 6px solid #666;\n      border-bottom: 0; }\n    .tabulator-print-table .tabulator-print-table-group.tabulator-group-level-1 td {\n      padding-left: 30px !important; }\n    .tabulator-print-table .tabulator-print-table-group.tabulator-group-level-2 td {\n      padding-left: 50px !important; }\n    .tabulator-print-table .tabulator-print-table-group.tabulator-group-level-3 td {\n      padding-left: 70px !important; }\n    .tabulator-print-table .tabulator-print-table-group.tabulator-group-level-4 td {\n      padding-left: 90px !important; }\n    .tabulator-print-table .tabulator-print-table-group.tabulator-group-level-5 td {\n      padding-left: 110px !important; }\n    .tabulator-print-table .tabulator-print-table-group .tabulator-group-toggle {\n      display: inline-block; }\n    .tabulator-print-table .tabulator-print-table-group .tabulator-arrow {\n      display: inline-block;\n      width: 0;\n      height: 0;\n      margin-right: 16px;\n      border-top: 6px solid transparent;\n      border-bottom: 6px solid transparent;\n      border-right: 0;\n      border-left: 6px solid #666;\n      vertical-align: middle; }\n    .tabulator-print-table .tabulator-print-table-group span {\n      margin-left: 10px;\n      color: #d00; }\n  .tabulator-print-table .tabulator-data-tree-control {\n    display: inline-flex;\n    justify-content: center;\n    align-items: center;\n    vertical-align: middle;\n    height: 11px;\n    width: 11px;\n    margin-right: 5px;\n    border: 1px solid #fff;\n    border-radius: 2px;\n    background: rgba(0, 0, 0, 0.1);\n    overflow: hidden; }\n    @media (hover: hover) and (pointer: fine) {\n      .tabulator-print-table .tabulator-data-tree-control:hover {\n        cursor: pointer;\n        background: rgba(0, 0, 0, 0.2); } }\n    .tabulator-print-table .tabulator-data-tree-control .tabulator-data-tree-control-collapse {\n      display: inline-block;\n      position: relative;\n      height: 7px;\n      width: 1px;\n      background: transparent; }\n      .tabulator-print-table .tabulator-data-tree-control .tabulator-data-tree-control-collapse:after {\n        position: absolute;\n        content: \"\";\n        left: -3px;\n        top: 3px;\n        height: 1px;\n        width: 7px;\n        background: #fff; }\n    .tabulator-print-table .tabulator-data-tree-control .tabulator-data-tree-control-expand {\n      display: inline-block;\n      position: relative;\n      height: 7px;\n      width: 1px;\n      background: #fff; }\n      .tabulator-print-table .tabulator-data-tree-control .tabulator-data-tree-control-expand:after {\n        position: absolute;\n        content: \"\";\n        left: -3px;\n        top: 3px;\n        height: 1px;\n        width: 7px;\n        background: #fff; }\n\n.tabulator {\n  background-color: #222; }\n  .tabulator .tabulator-header .tabulator-col {\n    background-color: #333; }\n    .tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-title .tabulator-title-editor {\n      color: #fff; }\n    .tabulator .tabulator-header .tabulator-col .tabulator-header-filter input, .tabulator .tabulator-header .tabulator-col .tabulator-header-filter select {\n      border: 1px solid #999;\n      background: #444;\n      color: #fff; }\n  .tabulator .tabulator-header .tabulator-calcs-holder {\n    background: #1a1a1a !important; }\n    .tabulator .tabulator-header .tabulator-calcs-holder .tabulator-row {\n      background: #1a1a1a !important; }\n  .tabulator .tabulator-footer .tabulator-calcs-holder {\n    background: #262626 !important; }\n    .tabulator .tabulator-footer .tabulator-calcs-holder .tabulator-row {\n      background: #262626 !important; }\n  .tabulator .tabulator-footer .tabulator-spreadsheet-tabs .tabulator-spreadsheet-tab {\n    border-color: #aaa;\n    background: rgba(255, 255, 255, 0.2); }\n    .tabulator .tabulator-footer .tabulator-spreadsheet-tabs .tabulator-spreadsheet-tab.tabulator-spreadsheet-tab-active {\n      background: rgba(0, 0, 0, 0.2);\n      color: #fff; }\n  .tabulator .tabulator-footer .tabulator-paginator label {\n    color: #fff; }\n  .tabulator .tabulator-footer .tabulator-page-counter {\n    color: #fff; }\n  .tabulator .tabulator-footer .tabulator-page {\n    color: #333;\n    font-family: inherit;\n    font-weight: inherit;\n    font-size: inherit; }\n\n.tabulator-row.tabulator-group {\n  min-width: 100%;\n  color: #333; }\n  @media (hover: hover) and (pointer: fine) {\n    .tabulator-row.tabulator-group:hover {\n      cursor: pointer;\n      background-color: rgba(0, 0, 0, 0.1); } }\n  .tabulator-row.tabulator-group span {\n    color: #666; }\n\n.tabulator-toggle {\n  border-color: #000;\n  background: #333; }\n  .tabulator-toggle .tabulator-toggle-switch {\n    border-color: #000;\n    background: #232323; }\n\n.tabulator-edit-select-list {\n  background: #fff; }\n  .tabulator-edit-select-list .tabulator-edit-select-list-item {\n    color: #666; }\n    .tabulator-edit-select-list .tabulator-edit-select-list-item.active {\n      color: #999;\n      background: #444; }\n      .tabulator-edit-select-list .tabulator-edit-select-list-item.active.focused {\n        outline: 1px solid rgba(153, 153, 153, 0.5); }\n    .tabulator-edit-select-list .tabulator-edit-select-list-item.focused {\n      outline: 1px solid #444; }\n    @media (hover: hover) and (pointer: fine) {\n      .tabulator-edit-select-list .tabulator-edit-select-list-item:hover {\n        color: #999;\n        background: #666; } }\n\n.tabulator-print-table .tabulator-print-table-group {\n  color: #333; }\n"]}