## Sidecar binaries for Colony

This directory contains the sidecar binaries for Colony.  The binaries are used to provide additional functionality to Colony that is not available in the main Rust codebase.  The binaries are:

- dweb: A CLI tool for interacting with the Autonomi network.  Used to serve a local web server for Colony to connect to.

## Fetching the binaries

To fetch the latest binaries, run the `fetch_binaries.sh` script.  This will download the latest binaries from the [dweb](https://github.com/happybeing/dweb) repository and place them in this directory.

