// Frontend Integration Example for Colony Tauri Commands
import { invoke } from "@tauri-apps/api/core";

// Type definitions matching the Rust structs
interface PodInfo {
  id: string;
  name: string;
  address: string;
  created_date: string;
  last_modified: string;
}

interface CreatePodRequest {
  name: string;
}

interface UploadFileRequest {
  file_path: string;
}

interface DownloadFileRequest {
  address: string;
  destination_path: string;
}

// Pod Management Functions
export class PodManager {
  static async initialize(): Promise<string> {
    return await invoke("initialize_pod_manager");
  }

  static async createPod(name: string): Promise<PodInfo> {
    const request: CreatePodRequest = { name };
    return await invoke("create_pod", { request });
  }

  static async listPods(): Promise<PodInfo[]> {
    return await invoke("list_pods");
  }

  static async deletePod(podId: string): Promise<string> {
    return await invoke("delete_pod", { pod_id: podId });
  }
}

// Autonomi File Operations
export class AutonomiFiler {
  static async initialize(secretKey?: string): Promise<string> {
    return await invoke("initialize_autonomi_client", { secret_key: secretKey });
  }

  static async uploadFile(filePath: string): Promise<string> {
    const request: UploadFileRequest = { file_path: filePath };
    return await invoke("upload_file", { request });
  }

  static async downloadFile(address: string, destinationPath: string): Promise<string> {
    const request: DownloadFileRequest = { address, destination_path: destinationPath };
    return await invoke("download_file", { request });
  }
}

// Example usage in Svelte component
export async function exampleUsage() {
  try {
    // Initialize systems
    console.log(await PodManager.initialize());
    console.log(await AutonomiFiler.initialize());

    // Create a new pod
    const newPod = await PodManager.createPod("My First Pod");
    console.log("Created pod:", newPod);

    // List all pods
    const pods = await PodManager.listPods();
    console.log("All pods:", pods);

    // Upload a file
    const uploadResult = await AutonomiFiler.uploadFile("/path/to/file.txt");
    console.log("Upload result:", uploadResult);

    // Download a file
    const downloadResult = await AutonomiFiler.downloadFile(
      "network_address_here", 
      "/path/to/save/file.txt"
    );
    console.log("Download result:", downloadResult);

  } catch (error) {
    console.error("Error:", error);
  }
}

// Integration with existing UI components
export function integratePodManagement() {
  // For the "Create New Pod" modal
  const createPodButton = document.querySelector('.btn-primary');
  createPodButton?.addEventListener('click', async () => {
    const nameInput = document.querySelector('input[placeholder*="pod"]') as HTMLInputElement;
    if (nameInput?.value) {
      try {
        const pod = await PodManager.createPod(nameInput.value);
        console.log('Pod created:', pod);
        // Refresh the pod list in the UI
        await refreshPodList();
      } catch (error) {
        console.error('Failed to create pod:', error);
      }
    }
  });

  // For the upload buttons
  const uploadButtons = document.querySelectorAll('.btn-accent');
  uploadButtons.forEach(button => {
    button.addEventListener('click', async () => {
      // Open file picker and upload
      // This would integrate with Tauri's file dialog
    });
  });
}

async function refreshPodList() {
  try {
    const pods = await PodManager.listPods();
    // Update the table with new pod data
    updatePodTable(pods);
  } catch (error) {
    console.error('Failed to refresh pod list:', error);
  }
}

function updatePodTable(pods: PodInfo[]) {
  const tbody = document.querySelector('table tbody');
  if (tbody) {
    tbody.innerHTML = pods.map((pod, index) => `
      <tr>
        <th>${index + 1}</th>
        <td>${pod.name}</td>
        <td>${pod.address}</td>
        <td>${pod.created_date}</td>
        <td>${pod.last_modified}</td>
        <td>
          <button class="btn btn-accent" onclick="uploadPod('${pod.id}')">u</button>
          <button class="btn btn-warning" onclick="editPod('${pod.id}')">e</button>
          <button class="btn btn-error" onclick="deletePod('${pod.id}')">d</button>
        </td>
      </tr>
    `).join('');
  }
}

// Global functions for button handlers
(window as any).deletePod = async (podId: string) => {
  try {
    const result = await PodManager.deletePod(podId);
    console.log(result);
    await refreshPodList();
  } catch (error) {
    console.error('Failed to delete pod:', error);
  }
};
